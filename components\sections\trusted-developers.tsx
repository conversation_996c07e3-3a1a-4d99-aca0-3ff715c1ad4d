'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star, Quote } from 'lucide-react';

const developers = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Senior Full Stack Developer',
    company: 'TechCorp Solutions',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    testimonial: 'Dwelling Desire helped me find the perfect home office space. Their attention to detail and understanding of developer needs is exceptional.',
    rating: 5,
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Lead Software Engineer',
    company: 'InnovateLabs',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    testimonial: 'The team at Dwelling Desire understood exactly what I was looking for in a tech-friendly neighborhood. Highly recommended!',
    rating: 5,
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Frontend Developer',
    company: 'DesignHub',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    testimonial: 'Professional service with a deep understanding of remote work requirements. They found me the ideal property with great connectivity.',
    rating: 5,
  },
  {
    id: 4,
    name: 'David Park',
    role: 'DevOps Engineer',
    company: 'CloudTech',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    testimonial: 'Excellent experience working with Dwelling Desire. They helped me find a property that perfectly suits my work-from-home setup.',
    rating: 5,
  },
  {
    id: 5,
    name: 'Lisa Wang',
    role: 'Mobile App Developer',
    company: 'AppVentures',
    image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    testimonial: 'Their expertise in understanding tech professionals needs made the property search seamless and efficient.',
    rating: 5,
  },
  {
    id: 6,
    name: 'James Thompson',
    role: 'Backend Developer',
    company: 'DataFlow Systems',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    testimonial: 'Outstanding service! They found me a property with excellent infrastructure for my home lab setup.',
    rating: 5,
  },
];

export default function TrustedDevelopers() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Trusted by{' '}
            <span className="text-blue-600 dark:text-blue-400">
              Leading Developers
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Join hundreds of tech professionals who have found their perfect properties with us.
            Our expertise in understanding developer needs sets us apart.
          </p>
        </div>

        {/* Developer Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {developers.map((developer) => (
            <div key={developer.id}>
              <Card className="h-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                <CardContent className="p-6">
                  {/* Quote Icon */}
                  <div className="mb-4">
                    <Quote className="w-8 h-8 text-blue-600 dark:text-blue-400 opacity-60" />
                  </div>

                  {/* Testimonial */}
                  <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                    "{developer.testimonial}"
                  </p>

                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {[...Array(developer.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-4 h-4 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>

                  {/* Developer Info */}
                  <div className="flex items-center">
                    <Avatar className="w-12 h-12 mr-4">
                      <AvatarImage
                        src={developer.image}
                        alt={developer.name}
                      />
                      <AvatarFallback>
                        {developer.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        {developer.name}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {developer.role}
                      </p>
                      <p className="text-sm text-blue-600 dark:text-blue-400">
                        {developer.company}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg p-6">
              <h3 className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">500+</h3>
              <p className="text-gray-700 dark:text-gray-300">Tech Professionals Served</p>
            </div>
            <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg p-6">
              <h3 className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">98%</h3>
              <p className="text-gray-700 dark:text-gray-300">Client Satisfaction Rate</p>
            </div>
            <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg p-6">
              <h3 className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">50+</h3>
              <p className="text-gray-700 dark:text-gray-300">Tech Companies Partnered</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
